/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Task_App.h"

#define INDEX 2.5f //转向调试系数

static uint32_t lost_time = 0;
static uint8_t current_path = 0;  // 0:A->C, 1:C->B, 2:B->D, 3:D->A, 4:完成


/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(0); //目标基础速度
int16_t Data_MotorEncoder[2] = {0}; //四个电机的编码值 左前 右前 左后 右后
MOTOR_Def_t *Motor[2] = {&Motor_Font_Left, &Motor_Font_Right}; //电机实例

/*Data Tracker*/
uint8_t Data_Tracker_Input[8] = {TRACK_OFF}; //循迹模块的输入值
_iq Data_Tracker_Offset = _IQ(0); //循迹偏差
PID_Def_t Data_Tracker_PID; //转向环PID

//wit - 保留必要变量以维持接口兼容性
float Data_wit_Offset = 0.0f; //角度偏差 - 固化为0，移除陀螺仪影响

// 已清理的陀螺仪变量：Data_wit_Target, Data_wit_ControlEnabled, Data_wit_UserTarget

/*Test*/
bool Flag_LED = false;


void Task_Key(void *para);
void Task_LED(void *para);
void Task_Motor_PID(void *para);
void Task_Serial(void *para);
void Task_OLED(void *para);
void Task_Tracker(void *para);
// void Task_wit(void *para); // 已移除陀螺仪任务
void Task_AutoRecover(void *para);
void Serial_ReConfig(void); //串口重新配置函数

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Serial_Init(); //初始化串口
    Serial_ReConfig(); //重新配置串口为双向通信
    OLED_Init(); //OLED初始化
    // WIT_Init();  // 移除陀螺仪初始化

    // Data_wit_Target = _IQ(wit_data.yaw); // 移除陀螺仪目标角度设置
        
    Interrupt_Init(); //中断初始化

    Task_Add("Motor", Task_Motor_PID, 10, NULL, 0);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
    // Task_Add("wit",Task_wit, 10, NULL, 2);  // 移除陀螺仪任务注册
    Task_Add("Serial", Task_Serial, 50, NULL, 3); //添加串口通信任务
    Task_Add("Key", Task_Key, 20, NULL, 5);
    Task_Add("OLED", Task_OLED, 50, NULL, 4);
    Task_Add("AutoRecover", Task_AutoRecover, 10, NULL, 6);
}

//空闲任务函数
void Task_IdleFunction(void)
{
    if (!enable_group1_irq)
    {
        static uint16_t CNT = 0;
        if (++CNT == 5000)
        {
            CNT = 0;
        }
    }
}

// 在Task_OLED函数中添加详细诊断
void Task_OLED(void *para)
{
    OLED_Printf(0, 16*1, 8, "Data:%4.2f",wit_data.yaw);
    OLED_Printf(0, 16*2, 8, "SL:%4.2f SR:%4.2f",Motor_Font_Left.Motor_PID_Instance.Target,
                                         Motor_Font_Right.Motor_PID_Instance.Target);
    
}

//按键 20ms
void Task_Key(void *para)
{
    static uint8_t Key_Old;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    if (Key_Val==2)
    {
        Data_Motor_TarSpeed+=5*16777216;
        // LED_BOARD_TOGGLE();

    }
    else if(Key_Val==3)
    {
        WIT_CancelTargetControl();
        LED_BOARD_TOGGLE();
    }
}

//串口重新配置为双向通信
void Serial_ReConfig(void)
{
    // 重用现有的Serial.c基础设施，扩展为双向通信
    // Serial_Init()已经配置了基础的DMA通道

    // 确保接收DMA配置正确 - 重用Serial_RxData缓冲区
    extern uint8_t Serial_RxData[MAX_RX_LENGTH];
    DL_DMA_setSrcAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&UART_WIT_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&Serial_RxData[0]));
    DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH);
    DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID);

    // 发送DMA配置 - 重用现有配置
    DL_DMA_setDestAddr(DMA, DMA_CH_TX_CHAN_ID, (uint32_t)(&UART_WIT_INST->TXDATA));
    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);

    // 启用UART中断 - 支持双向通信
    NVIC_EnableIRQ(UART_WIT_INST_INT_IRQN);
}

//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    //获取电机速度
    for (uint8_t i = 0; i < 2; i++)
    {
        Motor_GetSpeed(Motor[i], 10);
    }

    //差速转向控制 - 偏差乘以转向系数
    _iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));

    // 左轮：偏右时减速，偏左时加速
    _iq Left_Speed = Data_Motor_TarSpeed - Steering_Adjustment - _IQ(Data_wit_Offset);
    // 右轮：偏右时加速，偏左时减速
    _iq Right_Speed = Data_Motor_TarSpeed + Steering_Adjustment + _IQ(Data_wit_Offset);

    // 设置目标速度
    Motor_Font_Left.Motor_PID_Instance.Target = _IQtoF(Left_Speed);
    Motor_Font_Right.Motor_PID_Instance.Target = _IQtoF(Right_Speed);

    //PID 计算
    for (uint8_t i = 0; i < 2; i++)
    {
        PID_SProsc(&Motor[i]->Motor_PID_Instance);
    }

    // 设置电机PWM输出
    for (uint8_t i = 0; i < 2; i++)
    {
        float output = Motor[i]->Motor_PID_Instance.Out;
        Motor_SetDuty(Motor[i], output);
    }
}

//灰度传感器读取、计算偏差 20ms
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = _IQ(0); 
    bool res = Tracker_Read(Data_Tracker_Input, &Temp); 
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}

// Task_wit函数已移除 - 陀螺仪功能完全清理

void Task_AutoRecover(void *para)
{
    static uint32_t line_found_time = 0;
    
    extern uint8_t ret;  // 来自Tracker.c的全局变量
    bool tracker_lost = (ret == 0 || ret == 255);  // 修复失线检测
    
    OLED_Printf(0, 16*3, 8, "current_path:%d",current_path);
    
    if (tracker_lost) {
        lost_time++;
        line_found_time = 0;
        
        // 失线超过100ms后开始角度控制
        if (lost_time > 20 && !WIT_IsTargetControlActive()) {
            float target_angle = wit_data.yaw;
            
            OLED_Printf(0, 16*0, 8, "target_angle %4.2f",target_angle);
            
            switch (current_path) {
                case 0:  // A到C段失线，准备转向B
                    current_path = 1;
                    target_angle = SMART_TURN_C_TO_B(wit_data.yaw);  // ✓ 智能避开边界问题
                    break;
                case 1:
                    WIT_SetTargetAngle(0.0f);
                    Delay(100);
                    WIT_CancelTargetControl();  // 取消角度控制
                    current_path = 2;  // C->B完成，开始B->D
                    break;
                case 2:  // B到D段失线，准备转向A
                    current_path = 3;
                    target_angle = SMART_TURN_B_TO_D(wit_data.yaw);  // ✓ 智能避开边界问题
                    break;                
                case 3:
                    Delay(10);
                break;
                default:
                    break;
            }
            
            // 角度范围处理
            if (target_angle > 180.0f) target_angle -= 360.0f;
            if (target_angle < -180.0f) target_angle += 360.0f;
            
            if (current_path < 4) {  // 未完成时才设置角度
                WIT_SetTargetAngle(target_angle);
            }
        }
    } else {
        // 重新检测到线
        lost_time = 0;
        line_found_time++;
        
        // 稳定检测到线50ms后恢复循迹
        if (line_found_time > 10) {
            if (WIT_IsTargetControlActive()) {
                WIT_CancelTargetControl();  // 取消角度控制
            }
                // 更新路径状态
            if (current_path == 1) {
                WIT_SetTargetAngle(0.0f);
                Delay(100);
                WIT_CancelTargetControl();  // 取消角度控制
                current_path = 2;  // C->B完成，开始B->D
            } else if (current_path == 3) {
                current_path = 4;  // D->A完成，任务结束
                Data_Motor_TarSpeed = _IQ(0);  // 停车
            }
        }
    }
}

//串口通信任务 50ms
void Task_Serial(void *para)
{
    static uint32_t tx_counter = 0; //发送计数器

    // 处理串口接收数据
    if (Flag_Serial_RXcplt) {
        Flag_Serial_RXcplt = false; //清除接收完成标志

        // 获取接收到的数据长度
        extern uint8_t Serial_RxData[MAX_RX_LENGTH];
        uint16_t rx_len = MAX_RX_LENGTH - DL_DMA_getTransferSize(DMA, DMA_CH_RX_CHAN_ID);

        // 简单的回显处理 - 将接收到的数据发送回去
        if (rx_len > 0) {
            MyPrintf("Received %d bytes: ", rx_len);
            for (uint16_t i = 0; i < rx_len && i < 50; i++) { //限制显示长度
                MyPrintf("%c", Serial_RxData[i]);
            }
            MyPrintf("\r\n");
        }

        // 重新启动接收DMA
        DL_DMA_setDestAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&Serial_RxData[0]));
        DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH);
        DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID);
    }

    // 定期发送状态信息 - 每2秒发送一次
    if (++tx_counter >= 40) { //50ms * 40 = 2000ms
        tx_counter = 0;

        // 发送系统状态信息
        MyPrintf("TI_CAR Status - Speed:%.2f, Tracker:%.2f\r\n",
                 _IQtoF(Data_Motor_TarSpeed), _IQtoF(Data_Tracker_Offset));
    }
}

